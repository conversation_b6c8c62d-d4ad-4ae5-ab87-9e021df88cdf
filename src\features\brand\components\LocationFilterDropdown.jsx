import React, { useState, useRef, useEffect } from 'react';
import DownIcon from '@assets/icon/down.svg';
import MapPinIcon from '@assets/icon/location.svg';
import { useLoading } from '@shared/components/UI/LoadingContext';
import { useSnackbar } from '@shared/components/UI/SnackbarContext';

/**
 * Mock API functions for location data
 * These simulate real API calls with Promise-based responses and realistic delays
 */
const mockLocationApi = {
  /**
   * Fetch countries based on search query
   * @param {string} query - Search query for countries
   * @returns {Promise<Array>} Promise resolving to array of countries
   */
  searchCountries: async (query) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const mockCountries = [
      { name: "India", groups: ["Tier 1", "Tier 2", "Tier 3", "Tier 4"] },
      { name: "Thailand", groups: ["Province 1", "Province 2"] },
      { name: "USA", groups: ["Metropolitan", "Suburban", "Rural"] },
      { name: "Germany", groups: [] },
      { name: "Brazil", groups: ["Major Cities", "Secondary Cities"] },
      { name: "United Kingdom", groups: ["London", "Manchester", "Birmingham"] },
      { name: "Canada", groups: ["Major Cities", "Secondary Cities"] },
      { name: "Australia", groups: ["Capital Cities", "Regional Areas"] },
      { name: "France", groups: [] },
      { name: "Japan", groups: ["Tokyo Metro", "Osaka Metro", "Other Cities"] }
    ];

    if (!query) return mockCountries;

    return mockCountries.filter(country =>
      country.name.toLowerCase().includes(query.toLowerCase())
    );
  },

  /**
   * Fetch cities/states for a selected country
   * @param {string} countryName - Name of the selected country
   * @param {string} query - Search query for cities
   * @returns {Promise<Array>} Promise resolving to array of cities
   */
  searchCities: async (countryName, query = '') => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));

    const mockCitiesData = {
      "India": [
        // Tier 1 - Cities and States
        { name: "Mumbai", group: "Tier 1", type: "city" },
        { name: "Delhi", group: "Tier 1", type: "city" },
        { name: "Bangalore", group: "Tier 1", type: "city" },
        { name: "Chennai", group: "Tier 1", type: "city" },
        { name: "Maharashtra", group: "Tier 1", type: "state" },
        { name: "Karnataka", group: "Tier 1", type: "state" },

        // Tier 2 - Cities and States
        { name: "Pune", group: "Tier 2", type: "city" },
        { name: "Hyderabad", group: "Tier 2", type: "city" },
        { name: "Ahmedabad", group: "Tier 2", type: "city" },
        { name: "Kolkata", group: "Tier 2", type: "city" },
        { name: "Kochi", group: "Tier 2", type: "city" },
        { name: "Gujarat", group: "Tier 2", type: "state" },
        { name: "West Bengal", group: "Tier 2", type: "state" },
        { name: "Kerala", group: "Tier 2", type: "state" },

        // Tier 3 - Cities and States
        { name: "Jaipur", group: "Tier 3", type: "city" },
        { name: "Lucknow", group: "Tier 3", type: "city" },
        { name: "Indore", group: "Tier 3", type: "city" },
        { name: "Bhopal", group: "Tier 3", type: "city" },
        { name: "Rajasthan", group: "Tier 3", type: "state" },
        { name: "Uttar Pradesh", group: "Tier 3", type: "state" },
        { name: "Madhya Pradesh", group: "Tier 3", type: "state" },

        // Tier 4 - Cities and States
        { name: "Kanpur", group: "Tier 4", type: "city" },
        { name: "Nagpur", group: "Tier 4", type: "city" },
        { name: "Patna", group: "Tier 4", type: "city" },
        { name: "Agra", group: "Tier 4", type: "city" },
        { name: "Bihar", group: "Tier 4", type: "state" },
        { name: "Jharkhand", group: "Tier 4", type: "state" }
      ],
      "Thailand": [
        { name: "Bangkok", group: "Province 1", type: "city" },
        { name: "Chiang Mai", group: "Province 2", type: "city" },
        { name: "Phuket", group: "Province 1", type: "city" },
        { name: "Pattaya", group: "Province 2", type: "city" },
        { name: "Central Thailand", group: "Province 1", type: "state" },
        { name: "Northern Thailand", group: "Province 2", type: "state" }
      ],
      "USA": [
        { name: "New York", group: "Metropolitan", type: "city" },
        { name: "Los Angeles", group: "Metropolitan", type: "city" },
        { name: "Chicago", group: "Metropolitan", type: "city" },
        { name: "Houston", group: "Suburban", type: "city" },
        { name: "Phoenix", group: "Suburban", type: "city" },
        { name: "Philadelphia", group: "Suburban", type: "city" },
        { name: "San Antonio", group: "Rural", type: "city" },
        { name: "San Diego", group: "Rural", type: "city" },
        { name: "New York State", group: "Metropolitan", type: "state" },
        { name: "California", group: "Metropolitan", type: "state" },
        { name: "Texas", group: "Suburban", type: "state" },
        { name: "Arizona", group: "Rural", type: "state" }
      ],
      "Germany": [
        { name: "Berlin", group: "", type: "city" },
        { name: "Munich", group: "", type: "city" },
        { name: "Hamburg", group: "", type: "city" },
        { name: "Cologne", group: "", type: "city" },
        { name: "Bavaria", group: "", type: "state" },
        { name: "North Rhine-Westphalia", group: "", type: "state" }
      ],
      "Brazil": [
        { name: "São Paulo", group: "Major Cities", type: "city" },
        { name: "Rio de Janeiro", group: "Major Cities", type: "city" },
        { name: "Brasília", group: "Secondary Cities", type: "city" },
        { name: "Salvador", group: "Secondary Cities", type: "city" },
        { name: "São Paulo State", group: "Major Cities", type: "state" },
        { name: "Rio de Janeiro State", group: "Major Cities", type: "state" },
        { name: "Bahia", group: "Secondary Cities", type: "state" }
      ]
    };

    const cities = mockCitiesData[countryName] || [];

    if (!query) return cities;

    return cities.filter(city =>
      city.name.toLowerCase().includes(query.toLowerCase())
    );
  }
};

/**
 * Custom hook for debouncing values
 * @param {any} value - Value to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {any} Debounced value
 */
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Dynamic LocationFilterDropdown Component
 *
 * A two-phase location filter that allows users to:
 * 1. Search and select a country with typeahead functionality
 * 2. Search and select cities/states within the selected country
 *
 * Features:
 * - Debounced search (300ms delay) for optimal API performance
 * - Loading states during API requests
 * - Error handling with user feedback
 * - Group display logic (conditional based on country data)
 * - Reusable and configurable design
 *
 * @param {Function} onSelect - Callback function when location is selected
 * @param {Object} selectedLocation - Currently selected location object
 * @param {Array} selectedLocations - Array of selected locations (for multi-select)
 * @param {boolean} multiSelect - Whether to allow multiple selections
 * @param {string} placeholder - Placeholder text for the dropdown button
 * @param {string} className - Additional CSS classes
 */
const LocationFilterDropdown = ({
  onSelect,
  selectedLocation = null,
  selectedLocations = [],
  multiSelect = false,
  placeholder = "Select Location",
  className = ""
}) => {
  // Component state
  const [isOpen, setIsOpen] = useState(false);
  const [phase, setPhase] = useState('country'); // 'country', 'location', 'group-detail'
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [countryQuery, setCountryQuery] = useState('');
  const [cityQuery, setCityQuery] = useState('');
  const [countries, setCountries] = useState([]);
  const [cities, setCities] = useState([]);
  const [expandedGroups, setExpandedGroups] = useState({});
  const [groupSelections, setGroupSelections] = useState({}); // Track group-level selections
  const [pendingIndividualSelections, setPendingIndividualSelections] = useState([]); // Track pending individual selections
  const [isLoadingCountries, setIsLoadingCountries] = useState(false);
  const [isLoadingCities, setIsLoadingCities] = useState(false);
  const [error, setError] = useState(null);

  // Refs and hooks
  const dropdownRef = useRef(null);
  const { showSnackbar } = useSnackbar();

  // Debounced search queries
  const debouncedCountryQuery = useDebounce(countryQuery, 300);
  const debouncedCityQuery = useDebounce(cityQuery, 300);

  // Fetch countries when country query changes
  useEffect(() => {
    const fetchCountries = async () => {
      if (phase !== 'country') return;

      setIsLoadingCountries(true);
      setError(null);

      try {
        const results = await mockLocationApi.searchCountries(debouncedCountryQuery);
        setCountries(results);
      } catch (err) {
        setError('Failed to fetch countries');
        showSnackbar('Failed to fetch countries', 'error');
      } finally {
        setIsLoadingCountries(false);
      }
    };

    fetchCountries();
  }, [debouncedCountryQuery, phase, showSnackbar]);

  // Fetch cities when city query or selected country changes
  useEffect(() => {
    const fetchCities = async () => {
      if ((phase !== 'location' && phase !== 'group-detail') || !selectedCountry) return;

      setIsLoadingCities(true);
      setError(null);

      try {
        const results = await mockLocationApi.searchCities(selectedCountry.name, debouncedCityQuery);
        setCities(results);
      } catch (err) {
        setError('Failed to fetch cities');
        showSnackbar('Failed to fetch cities', 'error');
      } finally {
        setIsLoadingCities(false);
      }
    };

    fetchCities();
  }, [debouncedCityQuery, selectedCountry, phase, showSnackbar]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Initialize countries on first open
  useEffect(() => {
    if (isOpen && phase === 'country' && countries.length === 0) {
      setCountryQuery('');
    }
  }, [isOpen, phase, countries.length]);

  // Note: Removed restorePreviousSelection function as dropdown now always starts at Phase 1

  // Note: Removed auto-restoration to always start at Phase 1 (country selection)
  // Users can manually navigate to their desired country and phase

  // Handle dropdown toggle
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // Always reset to country phase (Phase 1) when opening dropdown
      setPhase('country');
      setSelectedCountry(null);
      setSelectedGroup(null);
      setCountryQuery('');
      setCityQuery('');
      setExpandedGroups({});
      setGroupSelections({});
      setPendingIndividualSelections([]);
      setError(null);
    }
  };

  // Handle country selection
  const handleCountrySelect = (country) => {
    setSelectedCountry(country);
    setPhase('location');
    setCityQuery('');
    setError(null);
  };

  // Handle individual city/state selection - add to pending selections
  const handleIndividualSelect = (item) => {
    const locationData = {
      country: selectedCountry.name,
      city: item.name,
      type: item.type,
      group: item.group,
      fullLocation: `${item.name} (${item.type.charAt(0).toUpperCase() + item.type.slice(1)}), ${selectedCountry.name}`,
      selectionType: 'individual'
    };

    // Check if item is already in pending selections
    const isAlreadySelected = pendingIndividualSelections.some(selected =>
      selected.city === item.name &&
      selected.type === item.type &&
      selected.country === selectedCountry.name
    );

    if (isAlreadySelected) {
      // Remove from pending selections if already selected
      setPendingIndividualSelections(prev =>
        prev.filter(selected =>
          !(selected.city === item.name &&
            selected.type === item.type &&
            selected.country === selectedCountry.name)
        )
      );
    } else {
      // Add to pending selections
      setPendingIndividualSelections(prev => [...prev, locationData]);
    }

    // Clear search input to allow searching for more items
    setCityQuery('');

    // Keep dropdown open for multi-selection
  };

  // Handle group selection (expand/collapse)
  const handleGroupToggle = (groupName) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupName]: !prev[groupName]
    }));
  };

  // Handle group detail selection
  const handleGroupDetailSelect = (groupName) => {
    setSelectedGroup(groupName);

    // If there's an existing group selection, restore it
    const existingSelection = selectedLocations.find(selected =>
      selected.selectionType === 'group' &&
      selected.group === groupName &&
      selected.country === selectedCountry?.name
    );

    if (existingSelection) {
      // Restore the existing group selection
      setGroupSelections({
        ...groupSelections,
        [groupName]: {
          selectedItems: existingSelection.selectedItems || [],
          excludedItems: existingSelection.excludedItems || []
        }
      });
    } else if (!groupSelections[groupName]) {
      // Initialize empty selection if none exists
      setGroupSelections({
        ...groupSelections,
        [groupName]: {
          selectedItems: [],
          excludedItems: []
        }
      });
    }

    setPhase('group-detail');
  };

  // Handle master checkbox toggle for select/deselect all in group
  const handleMasterCheckboxToggle = (groupName) => {
    const groupItems = cities.filter(city => city.group === groupName);
    const selection = groupSelections[groupName] || { selectedItems: [], excludedItems: [] };
    const selectedCount = selection.selectedItems.length;
    const totalCount = groupItems.length;

    // If all items are selected, deselect all
    // If no items or some items are selected, select all
    const shouldSelectAll = selectedCount < totalCount;

    setGroupSelections(prev => ({
      ...prev,
      [groupName]: {
        selectedItems: shouldSelectAll ? groupItems : [],
        excludedItems: []
      }
    }));
  };

  // Get master checkbox state for a group
  const getMasterCheckboxState = (groupName) => {
    const groupItems = cities.filter(city => city.group === groupName);
    const selection = groupSelections[groupName] || { selectedItems: [], excludedItems: [] };
    const selectedCount = selection.selectedItems.length;
    const totalCount = groupItems.length;

    if (selectedCount === 0) {
      return 'unchecked';
    } else if (selectedCount === totalCount) {
      return 'checked';
    } else {
      return 'indeterminate';
    }
  };

  // Handle individual item toggle within group
  const handleGroupItemToggle = (groupName, item) => {
    setGroupSelections(prev => {
      const currentSelection = prev[groupName] || { selectedItems: [], excludedItems: [] };
      const isSelected = currentSelection.selectedItems.some(selected => selected.name === item.name);

      if (isSelected) {
        // Remove from selected, add to excluded
        return {
          ...prev,
          [groupName]: {
            selectedItems: currentSelection.selectedItems.filter(selected => selected.name !== item.name),
            excludedItems: [...currentSelection.excludedItems, item]
          }
        };
      } else {
        // Add to selected, remove from excluded
        return {
          ...prev,
          [groupName]: {
            selectedItems: [...currentSelection.selectedItems, item],
            excludedItems: currentSelection.excludedItems.filter(excluded => excluded.name !== item.name)
          }
        };
      }
    });
  };

  // Handle group selection confirmation - return to Phase 2 for multi-group selection
  const handleGroupSelectionConfirm = () => {
    // Keep the group selection in pending state and return to location phase
    setPhase('location');
    setSelectedGroup(null);

    // The group selection is already stored in groupSelections state
    // We'll commit all selections when user clicks "Apply All Selections"
  };

  // Reset to initial state
  const resetToInitialState = () => {
    setPhase('country');
    setSelectedCountry(null);
    setSelectedGroup(null);
    setCountryQuery('');
    setCityQuery('');
    setExpandedGroups({});
    setGroupSelections({});
    setPendingIndividualSelections([]);
  };

  // Handle back navigation
  const handleBackToCountry = () => {
    setPhase('country');
    setSelectedCountry(null);
    setSelectedGroup(null);
    setCityQuery('');
    setExpandedGroups({});
    setGroupSelections({});
    setError(null);
  };

  // Handle back to location phase
  const handleBackToLocation = () => {
    setPhase('location');
    setSelectedGroup(null);
  };

  // Handle applying all pending selections and closing dropdown
  const handleApplyAllSelections = () => {
    // Commit all pending individual selections
    pendingIndividualSelections.forEach(locationData => {
      onSelect(locationData);
    });

    // Commit all pending group selections
    Object.entries(groupSelections).forEach(([groupName, selection]) => {
      if (selection.selectedItems.length > 0) {
        const groupData = {
          country: selectedCountry.name,
          group: groupName,
          selectedItems: selection.selectedItems,
          excludedItems: selection.excludedItems,
          selectionType: 'group'
        };
        onSelect(groupData);
      }
    });

    // Close dropdown and reset state
    setIsOpen(false);
    resetToInitialState();
  };

  // Get total count of pending selections across all groups and individual selections
  const getTotalPendingSelections = () => {
    const groupPendingCount = Object.values(groupSelections).reduce((total, selection) => {
      return total + selection.selectedItems.length;
    }, 0);

    const individualPendingCount = pendingIndividualSelections.length;

    return groupPendingCount + individualPendingCount;
  };

  // Check if there are any pending selections
  const hasPendingSelections = () => {
    return getTotalPendingSelections() > 0;
  };

  // Clear all selections
  const handleClear = () => {
    onSelect(null);
    setIsOpen(false);
    resetToInitialState();
  };

  // Display selected count or selected location
  const getDisplayText = () => {
    if (selectedLocations.length > 0) {
      // Get total count of all selected locations
      let totalSelectedLocations = 0;
      const countries = new Set();

      selectedLocations.forEach(selection => {
        countries.add(selection.country);
        if (selection.selectionType === 'individual') {
          totalSelectedLocations += 1;
        } else if (selection.selectionType === 'group') {
          totalSelectedLocations += selection.selectedItems.length;
        }
      });

      // Add pending group selections
      const pendingSelections = getTotalPendingSelections();
      totalSelectedLocations += pendingSelections;

      if (countries.size === 1) {
        const country = Array.from(countries)[0];
        const lastSelection = selectedLocations[selectedLocations.length - 1];

        if (selectedLocations.length === 1 && lastSelection.selectionType === 'individual') {
          return `${lastSelection.city} (${lastSelection.type})`;
        } else if (selectedLocations.length === 1 && lastSelection.selectionType === 'group') {
          const selectedCount = lastSelection.selectedItems.length;
          return `${lastSelection.group} (${selectedCount} locations)`;
        } else {
          return `${country} (${totalSelectedLocations} locations)`;
        }
      } else {
        return `${totalSelectedLocations} locations in ${countries.size} countries`;
      }
    }
    return placeholder;
  };

  // Get items by group
  const getItemsByGroup = (groupName) => {
    return cities.filter(item => item.group === groupName);
  };

  // Helper function to get selection summary for a country
  const getCountrySelectionSummary = (countryName) => {
    // Get individual selections for this country
    const individualSelections = selectedLocations.filter(selected =>
      selected.selectionType === 'individual' &&
      selected.country === countryName
    );

    // Get group selections for this country
    const committedGroupSelections = selectedLocations.filter(selected =>
      selected.selectionType === 'group' &&
      selected.country === countryName
    );

    // Get pending group selections for this country (if currently selected)
    const pendingGroupSelections = selectedCountry?.name === countryName ?
      Object.entries(groupSelections).filter(([, selection]) =>
        selection.selectedItems.length > 0
      ) : [];

    const totalIndividual = individualSelections.length;
    const totalGroupSelections = committedGroupSelections.length;
    const totalPendingGroups = pendingGroupSelections.length;

    if (totalIndividual === 0 && totalGroupSelections === 0 && totalPendingGroups === 0) {
      return null;
    }

    // Calculate total selected locations
    let totalSelectedLocations = totalIndividual;

    // Add locations from committed group selections
    committedGroupSelections.forEach(groupSelection => {
      totalSelectedLocations += groupSelection.selectedItems.length;
    });

    // Add locations from pending group selections
    pendingGroupSelections.forEach(([, selection]) => {
      totalSelectedLocations += selection.selectedItems.length;
    });

    // Generate summary text
    const parts = [];

    if (totalIndividual > 0) {
      if (totalIndividual === 1) {
        parts.push(`${individualSelections[0].city}`);
      } else if (totalIndividual <= 3) {
        const names = individualSelections.map(s => s.city).join(', ');
        parts.push(names);
      } else {
        const firstTwo = individualSelections.slice(0, 2).map(s => s.city).join(', ');
        parts.push(`${firstTwo} + ${totalIndividual - 2} more`);
      }
    }

    if (totalGroupSelections > 0) {
      committedGroupSelections.forEach(groupSelection => {
        const selectedCount = groupSelection.selectedItems.length;
        const excludedCount = groupSelection.excludedItems.length;
        if (excludedCount > 0) {
          parts.push(`${groupSelection.group}: ${selectedCount} locations`);
        } else {
          parts.push(`${groupSelection.group} group`);
        }
      });
    }

    if (totalPendingGroups > 0) {
      pendingGroupSelections.forEach(([groupName, selection]) => {
        parts.push(`${groupName}: ${selection.selectedItems.length} pending`);
      });
    }

    return {
      totalLocations: totalSelectedLocations,
      summary: parts.join(', '),
      hasIndividual: totalIndividual > 0,
      hasGroups: totalGroupSelections > 0,
      hasPending: totalPendingGroups > 0
    };
  };



  // Helper function to check if an item is selected across all selection methods
  const isItemSelected = (item) => {
    // Check committed individual selections
    const isIndividuallySelected = selectedLocations.some(selected =>
      selected.selectionType === 'individual' &&
      selected.country === selectedCountry?.name &&
      selected.city === item.name &&
      selected.type === item.type
    );

    // Check pending individual selections
    const isPendingIndividualSelected = pendingIndividualSelections.some(selected =>
      selected.country === selectedCountry?.name &&
      selected.city === item.name &&
      selected.type === item.type
    );

    // Check active group selections (pending)
    const isActiveGroupSelected = Object.values(groupSelections).some(groupSelection =>
      groupSelection.selectedItems.some(selectedItem =>
        selectedItem.name === item.name &&
        selectedItem.type === item.type &&
        selectedItem.group === item.group
      )
    );

    // Check committed group selections
    const isCommittedGroupSelected = selectedLocations.some(selected =>
      selected.selectionType === 'group' &&
      selected.country === selectedCountry?.name &&
      selected.selectedItems.some(selectedItem =>
        selectedItem.name === item.name &&
        selectedItem.type === item.type &&
        selectedItem.group === item.group
      )
    );

    return {
      isSelected: isIndividuallySelected || isPendingIndividualSelected || isActiveGroupSelected || isCommittedGroupSelected,
      selectionType: isIndividuallySelected ? 'individual' :
                    isPendingIndividualSelected ? 'pending-individual' :
                    isCommittedGroupSelected ? 'committed-group' :
                    isActiveGroupSelected ? 'active-group' : null
    };
  };

  // Helper function to get selection source for a specific group
  const isItemSelectedInGroup = (item, groupName) => {
    // Check if item is selected in the specific group's current selection
    const groupSelection = groupSelections[groupName];
    const isSelectedInCurrentGroup = groupSelection &&
      groupSelection.selectedItems.some(selectedItem =>
        selectedItem.name === item.name &&
        selectedItem.type === item.type
      );

    // Check if item is selected in committed group selections for this group
    const isCommittedSelected = selectedLocations.some(selected =>
      selected.selectionType === 'group' &&
      selected.group === groupName &&
      selected.selectedItems.some(selectedItem =>
        selectedItem.name === item.name &&
        selectedItem.type === item.type
      )
    );

    // Check if item is individually selected (committed)
    const isIndividuallySelected = selectedLocations.some(selected =>
      selected.selectionType === 'individual' &&
      selected.city === item.name &&
      selected.type === item.type
    );

    // Check if item is in pending individual selections
    const isPendingIndividualSelected = pendingIndividualSelections.some(selected =>
      selected.city === item.name &&
      selected.type === item.type
    );

    return {
      isSelected: isSelectedInCurrentGroup || isCommittedSelected || isIndividuallySelected || isPendingIndividualSelected,
      selectionType: isIndividuallySelected ? 'individual' :
                    isPendingIndividualSelected ? 'pending-individual' :
                    isCommittedSelected ? 'committed-group' :
                    isSelectedInCurrentGroup ? 'active-group' : null
    };
  };

  // Render loading spinner
  const renderLoadingSpinner = () => (
    <div className="flex items-center justify-center py-4">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-500"></div>
    </div>
  );

  // Render error state
  const renderError = () => (
    <div className="text-red-2 text-sm text-center py-3">
      {error}
    </div>
  );

  // Render country selection phase
  const renderCountryPhase = () => (
    <>
      <div className="px-4 pb-2">
        <h3 className="text-white text-lg font-medium mb-2">Select Country</h3>

        {/* Country Search Input */}
        <div className="mb-4">
          <input
            type="text"
            value={countryQuery}
            onChange={(e) => setCountryQuery(e.target.value)}
            placeholder="Search countries..."
            className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-brand-500"
            autoFocus
          />
        </div>

        {/* Country Options */}
        <div className="space-y-1 max-h-60 overflow-y-auto">
          {isLoadingCountries ? (
            renderLoadingSpinner()
          ) : error ? (
            renderError()
          ) : countries.length > 0 ? (
            countries.map((country) => {
              const selectionSummary = getCountrySelectionSummary(country.name);
              const hasSelections = selectionSummary !== null;

              return (
                <div
                  key={country.name}
                  className={`flex items-center justify-between py-2 px-2 cursor-pointer hover:bg-gray-800 rounded ${
                    hasSelections ? 'bg-blue-900/20 border border-brand-500/30' : ''
                  }`}
                  onClick={() => handleCountrySelect(country)}
                >
                  <div className="flex flex-col flex-1">
                    <div className="flex items-center gap-2">
                      {/* Selection indicator */}
                      {hasSelections && (
                        <div className="w-4 h-4 bg-brand-500 rounded-full flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-2.5 w-2.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                      <span className={`text-sm font-medium ${hasSelections ? 'text-blue-200' : 'text-white'}`}>
                        {country.name}
                      </span>
                      {/* Selection count badge */}
                      {hasSelections && (
                        <span className="bg-brand-600 text-blue-100 px-2 py-0.5 rounded-full text-xs font-medium">
                          {selectionSummary.totalLocations} selected
                        </span>
                      )}
                    </div>

                    {/* Country groups */}
                    {country.groups.length > 0 && (
                      <span className="text-gray-400 text-xs mt-1">
                        Groups: {country.groups.join(', ')}
                      </span>
                    )}

                    {/* Selection summary */}
                    {hasSelections && (
                      <div className="mt-1">
                        <span className="text-blue-300 text-xs">
                          {selectionSummary.summary}
                        </span>

                      </div>
                    )}
                  </div>

                  <svg className="h-4 w-4 text-gray-400 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              );
            })
          ) : (
            <div className="text-gray-400 text-sm text-center py-3">
              {countryQuery ? 'No countries found' : 'Start typing to search countries'}
            </div>
          )}
        </div>
      </div>
    </>
  );

  // Render location selection phase (combined search and group selection)
  const renderLocationPhase = () => {
    const hasGroups = selectedCountry?.groups && selectedCountry.groups.length > 0;

    return (
      <>
        <div className="px-4 pb-2">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <button
                onClick={handleBackToCountry}
                className="mr-2 p-1 hover:bg-gray-800 rounded"
              >
                <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h3 className="text-white text-lg font-medium">
                Locations in {selectedCountry?.name}
              </h3>
            </div>

            {/* Pending selections indicator */}
            {getTotalPendingSelections() > 0 && (
              <div className="flex items-center">
                <span className="text-blue-300 text-sm">
                  {getTotalPendingSelections()} pending
                </span>
              </div>
            )}
          </div>

          {/* Search Cities/States Input - Always at top */}
          <div className="mb-4">
            <input
              type="text"
              value={cityQuery}
              onChange={(e) => setCityQuery(e.target.value)}
              placeholder="Search cities or states..."
              className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-brand-500"
              autoFocus
            />
          </div>

          {/* Individual Search Results */}
          {cityQuery && (
            <div className="mb-4">
              <h4 className="text-gray-300 text-sm font-medium mb-2">Search Results</h4>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {isLoadingCities ? (
                  renderLoadingSpinner()
                ) : error ? (
                  renderError()
                ) : cities.filter(item =>
                    item.name.toLowerCase().includes(cityQuery.toLowerCase())
                  ).length > 0 ? (
                  cities.filter(item =>
                    item.name.toLowerCase().includes(cityQuery.toLowerCase())
                  ).map((item) => {
                    // Use helper function to check selection status
                    const { isSelected } = isItemSelected(item);

                    return (
                      <div
                        key={item.name}
                        className={`flex items-center justify-between py-2 px-2 cursor-pointer hover:bg-gray-800 rounded ${
                          isSelected ? 'bg-blue-900 border border-brand-500' : ''
                        }`}
                        onClick={() => handleIndividualSelect(item)}
                      >
                        <div className="flex items-center">
                          {/* Checkbox indicator for selected items */}
                          <div
                            className={`w-5 h-5 rounded border ${
                              isSelected
                                ? 'bg-brand-500 border-brand-500'
                                : 'bg-gray-700 border-gray-600'
                            } flex items-center justify-center mr-3`}
                          >
                            {isSelected && (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            )}
                          </div>
                          <div className="flex flex-col">
                            <span className={`text-sm ${isSelected ? 'text-blue-200 font-medium' : 'text-white'}`}>
                              {item.name} ({item.type.charAt(0).toUpperCase() + item.type.slice(1)})
                            </span>
                            <div className="flex items-center gap-2">
                              {item.group && (
                                <span className="text-gray-400 text-xs">
                                  Group: {item.group}
                                </span>
                              )}

                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-gray-400 text-sm text-center py-3">
                    No locations found
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Group Selection - Always visible if country has groups */}
          {hasGroups && (
            <div>
              <h4 className="text-gray-300 text-sm font-medium mb-2">Select by Group</h4>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {selectedCountry.groups.map((groupName) => {
                  const groupItems = getItemsByGroup(groupName);
                  const isExpanded = expandedGroups[groupName];

                  // Check if this group has any selections
                  const groupSelection = groupSelections[groupName];
                  const hasGroupSelection = groupSelection && groupSelection.selectedItems.length > 0;
                  const isGroupSelected = selectedLocations.some(selected =>
                    selected.selectionType === 'group' &&
                    selected.group === groupName &&
                    selected.country === selectedCountry?.name
                  );

                  // Get selection summary for display
                  const getSelectionSummary = () => {
                    if (isGroupSelected) {
                      const selected = selectedLocations.find(s =>
                        s.selectionType === 'group' && s.group === groupName
                      );
                      if (selected) {
                        const selectedCount = selected.selectedItems.length;
                        const excludedCount = selected.excludedItems.length;
                        let summary = `${selectedCount} of ${groupItems.length} selected`;
                        if (excludedCount > 0) {
                          summary += `, ${excludedCount} excluded`;
                        }
                        return summary;
                      }
                    } else if (hasGroupSelection) {
                      const selectedCount = groupSelection.selectedItems.length;
                      const excludedCount = groupSelection.excludedItems.length;
                      let summary = `${selectedCount} of ${groupItems.length} selected`;
                      if (excludedCount > 0) {
                        summary += `, ${excludedCount} excluded`;
                      }
                      return summary;
                    }
                    return `${groupItems.length} locations`;
                  };

                  return (
                    <div key={groupName} className={`border rounded ${
                      isGroupSelected ? 'border-brand-500 bg-blue-900/20' : 'border-gray-700'
                    }`}>
                      {/* Group Header */}
                      <div className={`flex items-center justify-between p-3 ${
                        isGroupSelected ? 'bg-blue-800/30' : 'bg-gray-800'
                      }`}>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleGroupToggle(groupName)}
                            className="text-gray-400 hover:text-white"
                          >
                            <svg
                              className={`h-4 w-4 transition-transform ${isExpanded ? 'transform rotate-90' : ''}`}
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </button>
                          <div className="flex items-center gap-2">
                            {/* Group selection indicator */}
                            {isGroupSelected && (
                              <div className="w-4 h-4 bg-brand-500 rounded-full flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-2.5 w-2.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                            )}
                            <span className={`font-medium ${isGroupSelected ? 'text-blue-200' : 'text-white'}`}>
                              {groupName}
                            </span>
                          </div>
                          <span className="text-gray-400 text-sm">({getSelectionSummary()})</span>
                        </div>
                        <button
                          onClick={() => handleGroupDetailSelect(groupName)}
                          className={`px-3 py-1 text-sm rounded transition-colors ${
                            isGroupSelected
                              ? 'bg-brand-500 text-white hover:bg-brand-600'
                              : 'bg-brand-600 text-white hover:bg-blue-700'
                          }`}
                        >
                          {isGroupSelected ? 'Edit Selection' : 'Select Group'}
                        </button>
                      </div>

                      {/* Group Items Preview */}
                      {isExpanded && (
                        <div className="p-3 bg-gray-900">
                          <div className="space-y-1 max-h-32 overflow-y-auto">
                            {groupItems.map((item) => {
                              // Use helper function to check selection status for this group
                              const { isSelected: isItemSelectedInThisGroup } = isItemSelectedInGroup(item, groupName);

                              return (
                                <div
                                  key={item.name}
                                  className={`flex items-center text-sm py-1 ${
                                    isItemSelectedInThisGroup ? 'text-blue-200' : 'text-gray-300'
                                  }`}
                                >
                                  {/* Selection indicator */}
                                  <div className={`w-3 h-3 rounded-full mr-2 ${
                                    isItemSelectedInThisGroup ? 'bg-brand-500' : 'bg-gray-600'
                                  }`} />
                                  <span>
                                    {item.name} ({item.type.charAt(0).toUpperCase() + item.type.slice(1)})
                                  </span>

                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* No groups fallback */}
          {!hasGroups && !cityQuery && (
            <div className="text-gray-400 text-sm text-center py-6">
              Start typing to search for cities and states
            </div>
          )}
        </div>
      </>
    );
  };

  // Render group detail phase (granular selection with exclusions)
  const renderGroupDetailPhase = () => {
    const groupItems = getItemsByGroup(selectedGroup);
    const selection = groupSelections[selectedGroup] || { selectedItems: [], excludedItems: [] };
    const selectedCount = selection.selectedItems.length;
    const totalCount = groupItems.length;

    return (
      <>
        <div className="px-4 pb-2">
          <div className="flex items-center mb-2">
            <button
              onClick={handleBackToLocation}
              className="mr-2 p-1 hover:bg-gray-800 rounded"
            >
              <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h3 className="text-white text-lg font-medium">
              {selectedGroup} Selection
            </h3>
          </div>

          {/* Selection Summary */}
          <div className="mb-4 p-3 bg-gray-800 rounded">
            <div className="text-white text-sm font-medium mb-1">
              {selectedCount} of {totalCount} locations selected
            </div>
            {selection.excludedItems.length > 0 && (
              <div className="text-gray-400 text-xs">
                Excluding: {selection.excludedItems.map(item => item.name).join(', ')}
              </div>
            )}
          </div>

          {/* Master Select All Checkbox */}
          <div className="mb-4">
            <div
              className="flex items-center cursor-pointer hover:bg-gray-800 rounded p-2"
              onClick={() => handleMasterCheckboxToggle(selectedGroup)}
            >
              <div
                className={`w-6 h-6 rounded border flex items-center justify-center mr-3 ${
                  getMasterCheckboxState(selectedGroup) === 'checked' || getMasterCheckboxState(selectedGroup) === 'indeterminate'
                    ? 'bg-brand-500 border-brand-500'
                    : 'bg-gray-700 border-gray-600'
                }`}
              >
                {getMasterCheckboxState(selectedGroup) === 'checked' && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
                {getMasterCheckboxState(selectedGroup) === 'indeterminate' && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <span className="text-white font-medium">
                Select All ({groupItems.length} locations)
              </span>
            </div>
          </div>

          {/* Individual Items with Checkboxes */}
          <div className="space-y-1 max-h-60 overflow-y-auto">
            {groupItems.map((item) => {
              // Use cross-selection helper to check if item is selected via any method
              const { isSelected } = isItemSelectedInGroup(item, selectedGroup);

              return (
                <div
                  key={item.name}
                  className="flex items-center justify-between py-2 px-2 cursor-pointer hover:bg-gray-800 rounded"
                  onClick={() => handleGroupItemToggle(selectedGroup, item)}
                >
                  <div className="flex items-center">
                    <div
                      className={`w-5 h-5 rounded border ${
                        isSelected
                          ? 'bg-brand-500 border-brand-500'
                          : 'bg-gray-700 border-gray-600'
                      } flex items-center justify-center mr-3`}
                    >
                      {isSelected && (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                    <div className="flex flex-col">
                      <span className="text-white text-sm">
                        {item.name} ({item.type.charAt(0).toUpperCase() + item.type.slice(1)})
                      </span>
                      <span className="text-gray-400 text-xs">
                        Group: {item.group}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Confirm Selection Button */}
          {selectedCount > 0 && (
            <div className="mt-4">
              <button
                onClick={handleGroupSelectionConfirm}
                className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                Add to Selection ({selectedCount} locations)
              </button>
            </div>
          )}
        </div>
      </>
    );
  };

  // Render appropriate phase
  const renderCurrentPhase = () => {
    switch (phase) {
      case 'country':
        return renderCountryPhase();
      case 'location':
        return renderLocationPhase();
      case 'group-detail':
        return renderGroupDetailPhase();
      default:
        return renderCountryPhase();
    }
  };

  // Main render
  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        className={`px-4 py-2.5 ${
          isOpen ? 'bg-gray-700' : 'bg-transparent'
        } border border-gray-500 rounded-lg flex items-center gap-1.5 text-sm text-gray-50 hover:text-gray-300 transition-colors cursor-pointer`}
        onClick={toggleDropdown}
      >
        <img src={MapPinIcon} alt="Location" className="w-3.5 h-3.5" />
        {getDisplayText()}
        <img
          src={DownIcon}
          alt="Dropdown"
          className={`w-3.5 h-3.5 ml-0.5 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`}
        />
      </button>

      {isOpen && (
        <div className="absolute mt-1 left-0 w-96 bg-gray-900 border border-gray-800 rounded-md shadow-lg z-50 py-2">
          {renderCurrentPhase()}

          {/* Footer */}
          <div className="border-t border-gray-800 p-3 flex justify-between">
            <button
              className="text-sm text-gray-400 hover:text-white transition-colors"
              onClick={handleClear}
            >
              Clear
            </button>

            <div className="flex items-center gap-2">
              {/* Apply All Selections button for pending selections */}
              {getTotalPendingSelections() > 0 && phase === 'location' && (
                <button
                  onClick={handleApplyAllSelections}
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                >
                  Apply All ({getTotalPendingSelections()})
                </button>
              )}

              <button
                className="text-sm text-brand-500 hover:text-blue-400 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationFilterDropdown;
