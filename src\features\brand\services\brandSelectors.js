import { useSelector } from 'react-redux';
import { RequestStatus } from '../../../app/store/enum';

/**
 * Custom hook for accessing brand-related state in components
 * This provides a clean interface for components to read brand state
 */
const useBrandSelectors = () => {
    // Get the brand state slice from the store
    const brandState = useSelector((state) => state.brand);

    return {
        // === BRAND MANAGEMENT DATA ===

        /**
         * Brand management data
         */
        allocatedBrands: brandState.allocatedBrands,
        organizationBrands: brandState.organizationBrands,
        organizationId: brandState.organizationId,
        selectedBrand: brandState.selectedBrand,

        // === CREATOR DISCOVERY DATA ===

        /**
         * Search results and metadata
         */
        searchResults: brandState.searchResults,
        searchProfiles: brandState.searchResults.profiles,
        searchMetadata: brandState.searchResults.metadata,
        lastSearchParams: brandState.searchResults.lastSearchParams,
        searchPagination: brandState.searchResults.pagination,

        /**
         * Search UI state
         */
        activeFilters: brandState.activeFilters,
        searchQuery: brandState.searchQuery,

        // === FILTER MANAGEMENT DATA ===

        /**
         * Filter metadata by channel
         */
        filterMetadata: brandState.filterMetadata,

        /**
         * Get filter metadata for a specific channel
         * @param {string} channel - Platform channel (instagram, youtube, tiktok)
         */
        getFilterMetadataForChannel: (channel) => brandState.filterMetadata.filter(filter => filter.channel === channel),

        /**
         * Saved filter sets
         */
        savedFilters: brandState.savedFilters,
        globalFilters: brandState.globalFilters,
        currentFilterSet: brandState.currentFilterSet,

        /**
         * Creator List sets
         */
        creatorLists: brandState.creatorLists,

        /**
         * Advanced filter data
         */
        transformedFilters: brandState.transformedFilters,
        cacheStats: brandState.cacheStats,

        // === REQUEST STATUS ===

        /**
         * General brand operations status
         */
        status: brandState.status,
        isLoading: brandState.status === RequestStatus.LOADING,
        isSucceeded: brandState.status === RequestStatus.SUCCEEDED,
        isFailed: brandState.status === RequestStatus.FAILED,
        isIdle: brandState.status === RequestStatus.IDLE,

        /**
         * Search operations status
         */
        searchStatus: brandState.searchStatus,
        isSearchLoading: brandState.searchStatus === RequestStatus.LOADING,
        isSearchSucceeded: brandState.searchStatus === RequestStatus.SUCCEEDED,
        isSearchFailed: brandState.searchStatus === RequestStatus.FAILED,
        isSearchIdle: brandState.searchStatus === RequestStatus.IDLE,

        /**
         * Filter operations status
         */
        filterStatus: brandState.filterStatus,
        isFilterLoading: brandState.filterStatus === RequestStatus.LOADING,
        isFilterSucceeded: brandState.filterStatus === RequestStatus.SUCCEEDED,
        isFilterFailed: brandState.filterStatus === RequestStatus.FAILED,
        isFilterIdle: brandState.filterStatus === RequestStatus.IDLE,

        /**
         * Saved filter operations status
         */
        savedFilterStatus: brandState.savedFilterStatus,
        isSavedFilterLoading: brandState.savedFilterStatus === RequestStatus.LOADING,
        isSavedFilterSucceeded: brandState.savedFilterStatus === RequestStatus.SUCCEEDED,
        isSavedFilterFailed: brandState.savedFilterStatus === RequestStatus.FAILED,
        isSavedFilterIdle: brandState.savedFilterStatus === RequestStatus.IDLE,

        /**
         * Cache operations status
         */
        cacheStatus: brandState.cacheStatus,
        isCacheLoading: brandState.cacheStatus === RequestStatus.LOADING,
        isCacheSucceeded: brandState.cacheStatus === RequestStatus.SUCCEEDED,
        isCacheFailed: brandState.cacheStatus === RequestStatus.FAILED,
        isCacheIdle: brandState.cacheStatus === RequestStatus.IDLE,



        // === ERROR INFORMATION ===

        /**
         * Error states
         */
        error: brandState.error,
        searchError: brandState.searchError,
        filterError: brandState.filterError,
        savedFilterError: brandState.savedFilterError,
        cacheError: brandState.cacheError,

        /**
         * Error existence checks
         */
        hasError: !!brandState.error,
        hasSearchError: !!brandState.searchError,
        hasFilterError: !!brandState.filterError,
        hasSavedFilterError: !!brandState.savedFilterError,
        hasCacheError: !!brandState.cacheError,
        hasAnyError: !!(
            brandState.error ||
            brandState.searchError ||
            brandState.filterError ||
            brandState.savedFilterError ||
            brandState.cacheError
        ),

        // === COMPUTED VALUES ===

        /**
         * Check if user has any brands
         */
        hasBrands: brandState.allocatedBrands.length > 0 || brandState.organizationBrands.length > 0,

        /**
         * Total brand count
         */
        totalBrandCount: brandState.allocatedBrands.length + brandState.organizationBrands.length,

        /**
         * Check if search has results
         */
        hasSearchResults: brandState.searchResults.profiles.length > 0,

        /**
         * Check if filters are available for a channel
         * @param {string} channel - Platform channel
         */
        hasFiltersForChannel: (channel) => !!brandState.filterMetadata[channel],

        /**
         * Check if user has saved filters
         */
        hasSavedFilters: brandState.savedFilters.length > 0,

        /**
         * Check if user has global filters
         */
        hasGlobalFilters: brandState.globalFilters.length > 0,

        /**
         * Check if filters have been transformed
         */
        hasTransformedFilters: !!brandState.transformedFilters,

        /**
         * Check if cache stats are available
         */
        hasCacheStats: !!brandState.cacheStats,

        /**
         * Check if any operation is currently loading
         */
        isAnyLoading: [
            brandState.status,
            brandState.searchStatus,
            brandState.filterStatus,
            brandState.savedFilterStatus,
            brandState.cacheStatus
        ].includes(RequestStatus.LOADING),
    };
};

// === STANDALONE SELECTORS ===
// These can be used directly with useSelector if needed

/**
 * Select allocated brands
 */
export const selectAllocatedBrands = (state) => state.brand.allocatedBrands;

/**
 * Select organization brands
 */
export const selectOrganizationBrands = (state) => state.brand.organizationBrands;

/**
 * Select search results
 */
export const selectSearchResults = (state) => state.brand.searchResults;

/**
 * Select filter metadata
 */
export const selectFilterMetadata = (state) => state.brand.filterMetadata;

/**
 * Select saved filters
 */
export const selectSavedFilters = (state) => state.brand.savedFilters;

/**
 * Select brand status
 */
export const selectBrandStatus = (state) => state.brand.status;

/**
 * Select search status
 */
export const selectSearchStatus = (state) => state.brand.searchStatus;

/**
 * Select global filters
 */
export const selectGlobalFilters = (state) => state.brand.globalFilters;

/**
 * Select current filter set
 */
export const selectCurrentFilterSet = (state) => state.brand.currentFilterSet;

/**
 * Select transformed filters
 */
export const selectTransformedFilters = (state) => state.brand.transformedFilters;

/**
 * Select cache stats
 */
export const selectCacheStats = (state) => state.brand.cacheStats;

/**
 * Select search pagination data
 */
export const selectSearchPagination = (state) => state.brand.searchResults.pagination;

/**
 * Select current page
 */
export const selectCurrentPage = (state) => state.brand.searchResults.pagination.currentPage;

/**
 * Select total pages
 */
export const selectTotalPages = (state) => state.brand.searchResults.pagination.totalPages;

/**
 * Select total results
 */
export const selectTotalResults = (state) => state.brand.searchResults.pagination.totalResults;



/**
 * Select cache status
 */
export const selectCacheStatus = (state) => state.brand.cacheStatus;

/**
 * Select if any brand operation is loading
 */
export const selectIsBrandLoading = (state) => [
    state.brand.status,
    state.brand.searchStatus,
    state.brand.filterStatus,
    state.brand.savedFilterStatus,
    state.brand.cacheStatus
].includes(RequestStatus.LOADING);

export default useBrandSelectors;
